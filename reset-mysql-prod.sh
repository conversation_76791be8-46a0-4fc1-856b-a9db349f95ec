#!/bin/bash

# 重置生产环境MySQL数据库脚本
# 警告：这会删除所有现有的MySQL数据！

set -e

echo "🚨 警告：此脚本将删除所有现有的MySQL数据！"
echo "请确保你已经备份了重要数据。"
read -p "是否继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消。"
    exit 1
fi

# 加载环境变量
if [ -f .env.prod ]; then
    export $(cat .env.prod | grep -v '^#' | xargs)
else
    echo "❌ 找不到 .env.prod 文件"
    exit 1
fi

echo "🛑 停止所有服务..."
docker-compose -f docker-compose.prod.yml down

echo "🗑️  删除MySQL数据卷..."
docker volume rm moodplay_mysql_prod_data 2>/dev/null || echo "数据卷不存在或已删除"

echo "🚀 启动MySQL服务..."
docker-compose -f docker-compose.prod.yml up -d mysql

echo "⏳ 等待MySQL启动完成..."
sleep 45

# 检查MySQL是否准备就绪
echo "🔍 检查MySQL连接..."
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker exec mysql mysql -u root -p${DB_PASSWORD} -e "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ MySQL连接成功！"
        break
    fi
    echo "等待MySQL启动... (尝试 $attempt/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ MySQL启动超时"
    exit 1
fi

echo "📋 执行数据库初始化脚本..."
docker cp backend/create.sql mysql:/tmp/create.sql
docker exec mysql mysql -u root -p${DB_PASSWORD} ${DB_NAME} -e "SOURCE /tmp/create.sql;"

echo "🚀 启动所有服务..."
docker-compose -f docker-compose.prod.yml up -d

echo "📊 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

echo "✅ MySQL重置完成！"
echo "🔍 你可以使用以下命令查看日志："
echo "   docker logs mysql -f"
echo "   docker logs moodplay-backend -f"
